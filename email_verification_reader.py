#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
163邮箱验证码读取工具
自动登录邮箱并获取最新验证码
"""

import imaplib
import email
from email.header import decode_header
import re
import ssl
from datetime import datetime
import time

class EmailVerificationReader:
    def __init__(self, email_address, password):
        self.email_address = email_address
        self.password = password
        self.imap_server = "imap.163.com"
        self.imap_port = 993
        self.mail = None
        
    def connect(self):
        """连接到163邮箱IMAP服务器"""
        try:
            print(f"🔗 正在连接到 {self.imap_server}:{self.imap_port}")

            # 创建SSL上下文，跳过证书验证
            context = ssl.create_default_context()
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE

            # 连接IMAP服务器
            self.mail = imaplib.IMAP4_SSL(self.imap_server, self.imap_port, ssl_context=context)
            
            print(f"📧 正在登录邮箱: {self.email_address}")
            
            # 登录邮箱
            result = self.mail.login(self.email_address, self.password)
            
            if result[0] == 'OK':
                print("✅ 邮箱登录成功！")
                return True
            else:
                print(f"❌ 登录失败: {result}")
                return False
                
        except Exception as e:
            print(f"❌ 连接失败: {str(e)}")
            return False
    
    def decode_mime_words(self, s):
        """解码MIME编码的字符串"""
        try:
            decoded_words = decode_header(s)
            decoded_string = ''
            for word, encoding in decoded_words:
                if isinstance(word, bytes):
                    if encoding:
                        decoded_string += word.decode(encoding)
                    else:
                        decoded_string += word.decode('utf-8', errors='ignore')
                else:
                    decoded_string += word
            return decoded_string
        except:
            return str(s)
    
    def extract_verification_code(self, text):
        """从邮件内容中提取验证码"""
        # 常见验证码模式
        patterns = [
            r'验证码[：:\s]*([A-Za-z0-9]{4,8})',
            r'验证码为[：:\s]*([A-Za-z0-9]{4,8})',
            r'验证码是[：:\s]*([A-Za-z0-9]{4,8})',
            r'code[：:\s]*([A-Za-z0-9]{4,8})',
            r'Code[：:\s]*([A-Za-z0-9]{4,8})',
            r'CODE[：:\s]*([A-Za-z0-9]{4,8})',
            r'验证码\s*([A-Za-z0-9]{4,8})',
            r'(\d{4,8})\s*为您的验证码',
            r'您的验证码是\s*([A-Za-z0-9]{4,8})',
            r'动态码[：:\s]*([A-Za-z0-9]{4,8})',
            r'校验码[：:\s]*([A-Za-z0-9]{4,8})',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                return matches[0]
        
        # 如果没有找到，尝试查找纯数字验证码
        number_pattern = r'\b(\d{4,8})\b'
        numbers = re.findall(number_pattern, text)
        if numbers:
            # 返回最可能的验证码（通常是4-6位数字）
            for num in numbers:
                if 4 <= len(num) <= 6:
                    return num
        
        return None
    
    def get_latest_verification_code(self):
        """获取最新的验证码"""
        try:
            # 选择收件箱
            result = self.mail.select('INBOX')
            if result[0] != 'OK':
                print(f"❌ 选择收件箱失败: {result}")
                return None

            print("📬 正在搜索最新邮件...")

            # 搜索最近的邮件（最近1天）
            search_criteria = 'ALL'
            result, message_ids = self.mail.search(None, search_criteria)
            
            if result != 'OK':
                print("❌ 搜索邮件失败")
                return None
            
            message_ids = message_ids[0].split()
            if not message_ids:
                print("📭 收件箱为空")
                return None
            
            print(f"📊 找到 {len(message_ids)} 封邮件，正在检查最新邮件...")
            
            # 从最新邮件开始检查（倒序）
            for i, msg_id in enumerate(reversed(message_ids[-10:])):  # 只检查最新10封
                try:
                    # 获取邮件
                    result, msg_data = self.mail.fetch(msg_id, '(RFC822)')
                    if result != 'OK':
                        continue
                    
                    # 解析邮件
                    email_body = msg_data[0][1]
                    email_message = email.message_from_bytes(email_body)
                    
                    # 获取邮件主题
                    subject = self.decode_mime_words(email_message['Subject'] or '')
                    sender = self.decode_mime_words(email_message['From'] or '')
                    date = email_message['Date'] or ''
                    
                    print(f"\n📧 邮件 {i+1}:")
                    print(f"   发件人: {sender}")
                    print(f"   主题: {subject}")
                    print(f"   时间: {date}")
                    
                    # 检查是否是验证码邮件
                    verification_keywords = ['验证码', '验证', 'verification', 'code', '动态码', '校验码']
                    is_verification_email = any(keyword in subject.lower() for keyword in verification_keywords)
                    
                    if is_verification_email:
                        print("   🎯 这是验证码邮件！")
                    
                    # 获取邮件内容
                    email_content = ""
                    if email_message.is_multipart():
                        for part in email_message.walk():
                            if part.get_content_type() == "text/plain":
                                try:
                                    content = part.get_payload(decode=True)
                                    if content:
                                        email_content += content.decode('utf-8', errors='ignore')
                                except:
                                    pass
                            elif part.get_content_type() == "text/html":
                                try:
                                    content = part.get_payload(decode=True)
                                    if content:
                                        email_content += content.decode('utf-8', errors='ignore')
                                except:
                                    pass
                    else:
                        try:
                            content = email_message.get_payload(decode=True)
                            if content:
                                email_content = content.decode('utf-8', errors='ignore')
                        except:
                            pass
                    
                    # 提取验证码
                    verification_code = self.extract_verification_code(email_content)
                    if verification_code:
                        print(f"   ✅ 找到验证码: {verification_code}")
                        return {
                            'code': verification_code,
                            'subject': subject,
                            'sender': sender,
                            'date': date,
                            'content_preview': email_content[:200] + '...' if len(email_content) > 200 else email_content
                        }
                    else:
                        print("   ❌ 未找到验证码")
                        
                except Exception as e:
                    print(f"   ⚠️ 处理邮件时出错: {str(e)}")
                    continue
            
            print("\n❌ 在最新邮件中未找到验证码")
            return None
            
        except Exception as e:
            print(f"❌ 获取验证码时出错: {str(e)}")
            return None
    
    def close(self):
        """关闭邮箱连接"""
        if self.mail:
            try:
                self.mail.close()
                self.mail.logout()
                print("📪 邮箱连接已关闭")
            except:
                pass

def main():
    """主函数"""
    print("🚀 163邮箱验证码读取工具启动")
    print("=" * 50)
    
    # 邮箱配置
    email_address = "<EMAIL>"
    password = "zxczxczxc111"
    
    # 创建邮箱读取器
    reader = EmailVerificationReader(email_address, password)
    
    try:
        # 连接邮箱
        if not reader.connect():
            return
        
        # 获取最新验证码
        result = reader.get_latest_verification_code()
        
        if result:
            print("\n" + "=" * 50)
            print("🎉 成功找到验证码！")
            print("=" * 50)
            print(f"📧 验证码: {result['code']}")
            print(f"📧 邮件主题: {result['subject']}")
            print(f"📧 发件人: {result['sender']}")
            print(f"📧 时间: {result['date']}")
            print("\n📄 邮件内容预览:")
            print(result['content_preview'])
            print("=" * 50)
        else:
            print("\n❌ 未找到验证码邮件")
            print("💡 建议:")
            print("   1. 检查是否有新的验证码邮件")
            print("   2. 确认验证码邮件没有被误删")
            print("   3. 检查垃圾邮件文件夹")
    
    finally:
        reader.close()

if __name__ == "__main__":
    main()
